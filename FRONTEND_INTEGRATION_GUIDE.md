# 🔗 FRONTEND INTEGRATION GUIDE - LABEL SYSTEM

Hướng dẫn tích hợp Label System Backend với Frontend để đảm bảo hoạt động end-to-end flow.

## 📋 **API ENDPOINTS OVERVIEW**

### **Label Management APIs**
```javascript
// 1. L<PERSON>y danh sách màu có sẵn
GET /v1/labels/colors

// 2. Lấy labels của board
GET /v1/labels?boardId={boardId}

// 3. Tạo label mới
POST /v1/labels
Body: { boardId, name, color }

// 4. Lấy thông tin label
GET /v1/labels/{labelId}

// 5. Cập nhật label
PUT /v1/labels/{labelId}
Body: { name?, color? }

// 6. Xóa label
DELETE /v1/labels/{labelId}

// 7. Cập nhật labels cho card
PUT /v1/cards/{cardId}/labels
Body: { labelIds: [...] }
```

---

## 🔧 **FRONTEND API INTEGRATION**

### **1. Label API Service (`web/src/apis/labelAPIs.js`)**

```javascript
import { API_ROOT } from '~/utils/constants'
import { authorizeAxiosInstance } from '~/utils/authorizeAxios'

const API_INSTANCE = authorizeAxiosInstance()

// Lấy danh sách màu có sẵn cho labels
export const getLabelColorsAPI = async () => {
  const response = await API_INSTANCE.get(`${API_ROOT}/v1/labels/colors`)
  return response.data
}

// Lấy labels theo boardId
export const getLabelsByBoardIdAPI = async (boardId) => {
  const response = await API_INSTANCE.get(`${API_ROOT}/v1/labels?boardId=${boardId}`)
  return response.data
}

// Tạo label mới
export const createLabelAPI = async (labelData) => {
  const response = await API_INSTANCE.post(`${API_ROOT}/v1/labels`, labelData)
  return response.data
}

// Lấy thông tin label theo ID
export const getLabelByIdAPI = async (labelId) => {
  const response = await API_INSTANCE.get(`${API_ROOT}/v1/labels/${labelId}`)
  return response.data
}

// Cập nhật label
export const updateLabelAPI = async (labelId, updateData) => {
  const response = await API_INSTANCE.put(`${API_ROOT}/v1/labels/${labelId}`, updateData)
  return response.data
}

// Xóa label
export const deleteLabelAPI = async (labelId) => {
  const response = await API_INSTANCE.delete(`${API_ROOT}/v1/labels/${labelId}`)
  return response.data
}

// Cập nhật labels của card
export const updateCardLabelsAPI = async (cardId, labelIds) => {
  const response = await API_INSTANCE.put(`${API_ROOT}/v1/cards/${cardId}/labels`, { labelIds })
  return response.data
}
```

### **2. Cập nhật API Index (`web/src/apis/index.js`)**

```javascript
// ... existing exports

// Label APIs
export * from './labelAPIs'
```

---

## 🔄 **REDUX INTEGRATION**

### **1. Label Redux Slice (đã có sẵn trong Frontend)**

```javascript
// web/src/redux/activeBoard/activeBoardSlice.js
// Labels đã được tích hợp sẵn trong activeBoard state

// Cần cập nhật các actions để call API:
import { 
  getLabelsByBoardIdAPI, 
  createLabelAPI, 
  updateLabelAPI, 
  deleteLabelAPI 
} from '~/apis'

// Thunk action để fetch labels
export const fetchBoardLabels = createAsyncThunk(
  'activeBoard/fetchBoardLabels',
  async (boardId) => {
    const labels = await getLabelsByBoardIdAPI(boardId)
    return labels
  }
)

// Thunk action để tạo label
export const createNewLabel = createAsyncThunk(
  'activeBoard/createNewLabel',
  async (labelData) => {
    const newLabel = await createLabelAPI(labelData)
    return newLabel
  }
)

// Thunk action để cập nhật label
export const updateLabel = createAsyncThunk(
  'activeBoard/updateLabel',
  async ({ labelId, updateData }) => {
    const updatedLabel = await updateLabelAPI(labelId, updateData)
    return updatedLabel
  }
)

// Thunk action để xóa label
export const deleteLabel = createAsyncThunk(
  'activeBoard/deleteLabel',
  async (labelId) => {
    await deleteLabelAPI(labelId)
    return labelId
  }
)
```

### **2. Card Labels Redux Actions**

```javascript
// Thunk action để cập nhật labels của card
export const updateCardLabels = createAsyncThunk(
  'activeBoard/updateCardLabels',
  async ({ cardId, labelIds }) => {
    const updatedCard = await updateCardLabelsAPI(cardId, labelIds)
    return updatedCard
  }
)
```

---

## 🎨 **COMPONENT INTEGRATION**

### **1. LabelDialog Component Updates**

```javascript
// web/src/components/Modal/LabelDialog/LabelDialog.jsx
import { useDispatch, useSelector } from 'react-redux'
import { 
  fetchBoardLabels, 
  createNewLabel, 
  updateLabel, 
  deleteLabel,
  updateCardLabels 
} from '~/redux/activeBoard/activeBoardSlice'
import { getLabelColorsAPI } from '~/apis'

const LabelDialog = ({ cardId, open, onClose }) => {
  const dispatch = useDispatch()
  const board = useSelector(state => state.activeBoard.board)
  const [availableColors, setAvailableColors] = useState({})
  const [loading, setLoading] = useState(false)

  // Fetch available colors khi component mount
  useEffect(() => {
    const fetchColors = async () => {
      try {
        const colorsData = await getLabelColorsAPI()
        setAvailableColors(colorsData.colors)
      } catch (error) {
        console.error('Error fetching label colors:', error)
      }
    }
    fetchColors()
  }, [])

  // Fetch labels khi dialog mở
  useEffect(() => {
    if (open && board?._id) {
      dispatch(fetchBoardLabels(board._id))
    }
  }, [open, board?._id, dispatch])

  // Handle tạo label mới
  const handleCreateLabel = async (labelData) => {
    setLoading(true)
    try {
      await dispatch(createNewLabel({
        ...labelData,
        boardId: board._id
      })).unwrap()
      
      // Show success message
      toast.success('Label created successfully!')
    } catch (error) {
      toast.error('Error creating label: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // Handle cập nhật label
  const handleUpdateLabel = async (labelId, updateData) => {
    setLoading(true)
    try {
      await dispatch(updateLabel({ labelId, updateData })).unwrap()
      toast.success('Label updated successfully!')
    } catch (error) {
      toast.error('Error updating label: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // Handle xóa label
  const handleDeleteLabel = async (labelId) => {
    if (!window.confirm('Are you sure you want to delete this label? It will be removed from all cards.')) {
      return
    }

    setLoading(true)
    try {
      await dispatch(deleteLabel(labelId)).unwrap()
      toast.success('Label deleted successfully!')
    } catch (error) {
      toast.error('Error deleting label: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // Handle toggle label cho card
  const handleToggleLabel = async (labelId, isSelected) => {
    const card = board.cards.find(c => c._id === cardId)
    const currentLabelIds = card?.labelIds || []
    
    let newLabelIds
    if (isSelected) {
      // Remove label
      newLabelIds = currentLabelIds.filter(id => id !== labelId)
    } else {
      // Add label
      newLabelIds = [...currentLabelIds, labelId]
    }

    setLoading(true)
    try {
      await dispatch(updateCardLabels({ cardId, labelIds: newLabelIds })).unwrap()
      toast.success('Card labels updated successfully!')
    } catch (error) {
      toast.error('Error updating card labels: ' + error.message)
    } finally {
      setLoading(false)
    }
  }

  // ... render logic
}
```

### **2. Board Component Updates**

```javascript
// web/src/pages/Boards/_id.jsx
useEffect(() => {
  // Khi fetch board details, labels sẽ được populate sẵn từ backend
  // Không cần fetch labels riêng biệt
  if (boardId) {
    dispatch(fetchBoardDetailsAPI(boardId))
  }
}, [boardId, dispatch])
```

---

## 📡 **SOCKET.IO REAL-TIME UPDATES**

### **Backend Socket Events (cần implement)**

```javascript
// api/src/sockets/index.js
// Emit events khi có thay đổi labels

// Khi tạo label mới
socket.emit('FE_LABEL_CREATED', {
  boardId,
  label: newLabel
})

// Khi cập nhật label
socket.emit('FE_LABEL_UPDATED', {
  boardId,
  label: updatedLabel
})

// Khi xóa label
socket.emit('FE_LABEL_DELETED', {
  boardId,
  labelId: deletedLabelId
})

// Khi cập nhật labels của card
socket.emit('FE_CARD_LABELS_UPDATED', {
  boardId,
  card: updatedCard
})
```

### **Frontend Socket Listeners**

```javascript
// web/src/redux/activeBoard/activeBoardSlice.js
// Thêm socket listeners

socket.on('BE_LABEL_CREATED', (data) => {
  dispatch(addLabelToBoard(data.label))
})

socket.on('BE_LABEL_UPDATED', (data) => {
  dispatch(updateLabelInBoard(data.label))
})

socket.on('BE_LABEL_DELETED', (data) => {
  dispatch(removeLabelFromBoard(data.labelId))
})

socket.on('BE_CARD_LABELS_UPDATED', (data) => {
  dispatch(updateCardInBoard(data.card))
})
```

---

## 🧪 **END-TO-END TESTING SCENARIOS**

### **Scenario 1: Tạo Label và Gán cho Card**

```javascript
describe('Create Label and Assign to Card', () => {
  test('should create label and assign to card successfully', async () => {
    // 1. Mở LabelDialog
    fireEvent.click(screen.getByTestId('card-label-button'))
    
    // 2. Click "Create New Label"
    fireEvent.click(screen.getByText('Create New Label'))
    
    // 3. Nhập thông tin label
    fireEvent.change(screen.getByPlaceholderText('Label name'), {
      target: { value: 'Test Label' }
    })
    
    // 4. Chọn màu
    fireEvent.click(screen.getByTestId('color-picker-red'))
    
    // 5. Click Create
    fireEvent.click(screen.getByText('Create'))
    
    // 6. Verify label được tạo
    await waitFor(() => {
      expect(screen.getByText('Test Label')).toBeInTheDocument()
    })
    
    // 7. Toggle label cho card
    fireEvent.click(screen.getByTestId('label-test-label'))
    
    // 8. Verify card có label
    await waitFor(() => {
      expect(screen.getByTestId('card-label-test-label')).toBeInTheDocument()
    })
  })
})
```

### **Scenario 2: Xóa Label và Verify Cascade Delete**

```javascript
describe('Delete Label Cascade', () => {
  test('should remove label from all cards when deleted', async () => {
    // 1. Verify card có label
    expect(screen.getByTestId('card-label-test')).toBeInTheDocument()
    
    // 2. Mở LabelDialog
    fireEvent.click(screen.getByTestId('card-label-button'))
    
    // 3. Click delete label
    fireEvent.click(screen.getByTestId('delete-label-test'))
    
    // 4. Confirm delete
    fireEvent.click(screen.getByText('Confirm'))
    
    // 5. Verify label không còn trong danh sách
    await waitFor(() => {
      expect(screen.queryByText('Test Label')).not.toBeInTheDocument()
    })
    
    // 6. Verify label không còn trên card
    expect(screen.queryByTestId('card-label-test')).not.toBeInTheDocument()
  })
})
```

---

## 📊 **PERFORMANCE CONSIDERATIONS**

### **1. API Optimization**
- **Cache label colors**: Lưu vào localStorage, chỉ fetch 1 lần
- **Debounce search**: Khi search labels trong dialog
- **Optimistic updates**: Update UI ngay, rollback nếu API fail

### **2. Redux Optimization**
- **Memoized selectors**: Sử dụng reselect cho derived data
- **Normalized state**: Store labels by ID để tránh duplication
- **Batch updates**: Combine multiple actions nếu có thể

### **3. Component Optimization**
- **React.memo**: Cho LabelChip components
- **useMemo/useCallback**: Cho expensive computations
- **Lazy loading**: Load LabelDialog only when needed

---

## 🔒 **ERROR HANDLING BEST PRACTICES**

### **1. API Error Handling**
```javascript
try {
  await dispatch(createNewLabel(labelData)).unwrap()
  toast.success('Label created successfully!')
} catch (error) {
  // Handle specific error types
  if (error.status === 403) {
    toast.error('You do not have permission to create labels in this board')
  } else if (error.status === 422) {
    toast.error('Invalid label data: ' + error.message)
  } else {
    toast.error('Failed to create label. Please try again.')
  }
}
```

### **2. Loading States**
```javascript
const [labelOperations, setLabelOperations] = useState({
  creating: false,
  updating: {},
  deleting: {}
})

// Show loading cho specific operations
{labelOperations.creating && <CircularProgress size={20} />}
```

### **3. Offline Support**
```javascript
// Queue operations when offline
if (!navigator.onLine) {
  queueOperation('createLabel', labelData)
  toast.info('Operation queued. Will sync when online.')
  return
}
```

---

## ✅ **INTEGRATION CHECKLIST**

### **Backend Ready**
- [x] All API endpoints implemented and tested
- [x] Validation and error handling complete
- [x] Permission checks working
- [x] Database indexes created

### **Frontend Integration**
- [ ] API service functions implemented
- [ ] Redux actions and reducers updated
- [ ] Components connected to real APIs
- [ ] Loading states implemented
- [ ] Error handling added

### **Testing**
- [ ] Unit tests for API functions
- [ ] Integration tests for components
- [ ] E2E tests for user flows
- [ ] Performance testing completed

### **Real-time Features**
- [ ] Socket.IO events implemented
- [ ] Real-time updates working
- [ ] Conflict resolution handled

### **Production Ready**
- [ ] Error monitoring setup
- [ ] Performance monitoring
- [ ] Analytics tracking
- [ ] Documentation updated

**Lưu ý:** Đảm bảo test thoroughly trên staging environment trước khi deploy production. 