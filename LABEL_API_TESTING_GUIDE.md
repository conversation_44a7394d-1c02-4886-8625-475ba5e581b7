# 🧪 LABEL API TESTING GUIDE - POSTMAN

Hướng dẫn test các API endpoints của Label system bằng Postman/Thunder Client.

## 📋 **SETUP TRƯỚC KHI TEST**

### 1. **Environment Variables**
Tạo environment trong Postman với các biến:
```
baseURL: http://localhost:8017/v1
token: <your_jwt_token_here>
boardId: <valid_board_id>
labelId: <created_label_id>
cardId: <valid_card_id>
```

### 2. **Headers mặc định cho tất cả requests:**
```
Authorization: Bearer {{token}}
Content-Type: application/json
```

---

## 🏷️ **LABEL API ENDPOINTS TESTING**

### 1. **GET /v1/labels/colors - Lấy danh sách màu có sẵn**

**Request:**
```http
GET {{baseURL}}/labels/colors
Authorization: Bearer {{token}}
```

**Expected Response (200):**
```json
{
  "colors": {
    "BLUE": "#0079bf",
    "LIGHT_BLUE": "#54a3ff",
    "OCEAN": "#026aa7",
    "ORANGE": "#d29034",
    "PEACH": "#ffab4a",
    "AMBER": "#ff8f00",
    "GREEN": "#519839",
    "LIME": "#4bbf6b",
    "FOREST": "#2d5016",
    "RED": "#b04632",
    "CRIMSON": "#eb5a46",
    "CHERRY": "#c9372c",
    "PURPLE": "#89609e",
    "VIOLET": "#9c27b0",
    "INDIGO": "#6366f1",
    "PINK": "#cd5a91",
    "ROSE": "#e91e63",
    "MAGENTA": "#e040fb",
    "GREY": "#838c91",
    "DARK_GREY": "#4f4f4f",
    "NAVY": "#172b4d",
    "TEAL": "#00897b",
    "CYAN": "#00bcd4",
    "YELLOW": "#f9c74f"
  },
  "message": "Available label colors retrieved successfully"
}
```

---

### 2. **POST /v1/labels - Tạo label mới**

#### ✅ **Test Case 1: Tạo label hợp lệ**
**Request:**
```http
POST {{baseURL}}/labels
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "boardId": "{{boardId}}",
  "name": "Bug Fix",
  "color": "#eb5a46"
}
```

**Expected Response (201):**
```json
{
  "_id": "new_label_id",
  "boardId": "board_id",
  "name": "Bug Fix",
  "color": "#eb5a46",
  "createdAt": 1700000000000,
  "updatedAt": null,
  "_destroy": false
}
```

#### ❌ **Test Case 2: Tạo label với dữ liệu không hợp lệ**
**Request:**
```http
POST {{baseURL}}/labels
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "boardId": "invalid_board_id",
  "name": "Ab",
  "color": "invalid_color"
}
```

**Expected Response (422):**
```json
{
  "message": "\"boardId\" with value \"invalid_board_id\" fails to match the Object Id pattern. \"name\" length must be at least 3 characters long. Màu label phải là mã hex hợp lệ hoặc một trong các màu được định sẵn"
}
```

#### ❌ **Test Case 3: Tạo label không có quyền truy cập board**
**Request:**
```http
POST {{baseURL}}/labels
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "boardId": "673b2e5c9c84711234567890",
  "name": "Unauthorized Test",
  "color": "#0079bf"
}
```

**Expected Response (403):**
```json
{
  "message": "You do not have permission to access this board!"
}
```

---

### 3. **GET /v1/labels?boardId=xxx - Lấy labels theo board**

#### ✅ **Test Case 1: Lấy labels hợp lệ**
**Request:**
```http
GET {{baseURL}}/labels?boardId={{boardId}}
Authorization: Bearer {{token}}
```

**Expected Response (200):**
```json
[
  {
    "_id": "label_id_1",
    "boardId": "board_id",
    "name": "Bug Fix",
    "color": "#eb5a46",
    "createdAt": 1700000000000,
    "updatedAt": null,
    "_destroy": false
  },
  {
    "_id": "label_id_2",
    "boardId": "board_id",
    "name": "Feature",
    "color": "#519839",
    "createdAt": 1700000001000,
    "updatedAt": null,
    "_destroy": false
  }
]
```

#### ❌ **Test Case 2: Thiếu boardId**
**Request:**
```http
GET {{baseURL}}/labels
Authorization: Bearer {{token}}
```

**Expected Response (422):**
```json
{
  "message": "\"boardId\" is required"
}
```

---

### 4. **GET /v1/labels/:id - Lấy label theo ID**

#### ✅ **Test Case 1: Lấy label hợp lệ**
**Request:**
```http
GET {{baseURL}}/labels/{{labelId}}
Authorization: Bearer {{token}}
```

**Expected Response (200):**
```json
{
  "_id": "label_id",
  "boardId": "board_id",
  "name": "Bug Fix",
  "color": "#eb5a46",
  "createdAt": 1700000000000,
  "updatedAt": null,
  "_destroy": false
}
```

#### ❌ **Test Case 2: Label không tồn tại**
**Request:**
```http
GET {{baseURL}}/labels/673b2e5c9c84711234567890
Authorization: Bearer {{token}}
```

**Expected Response (404):**
```json
{
  "message": "Label not found!"
}
```

---

### 5. **PUT /v1/labels/:id - Cập nhật label**

#### ✅ **Test Case 1: Cập nhật hợp lệ**
**Request:**
```http
PUT {{baseURL}}/labels/{{labelId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Critical Bug",
  "color": "#b04632"
}
```

**Expected Response (200):**
```json
{
  "_id": "label_id",
  "boardId": "board_id",
  "name": "Critical Bug",
  "color": "#b04632",
  "createdAt": 1700000000000,
  "updatedAt": 1700000002000,
  "_destroy": false
}
```

#### ❌ **Test Case 2: Cập nhật với dữ liệu không hợp lệ**
**Request:**
```http
PUT {{baseURL}}/labels/{{labelId}}
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "Ab",
  "color": "invalid_color"
}
```

**Expected Response (422):**
```json
{
  "message": "\"name\" length must be at least 3 characters long. Màu label phải là mã hex hợp lệ hoặc một trong các màu được định sẵn"
}
```

---

### 6. **DELETE /v1/labels/:id - Xóa label**

#### ✅ **Test Case 1: Xóa label hợp lệ**
**Request:**
```http
DELETE {{baseURL}}/labels/{{labelId}}
Authorization: Bearer {{token}}
```

**Expected Response (200):**
```json
{
  "message": "Label deleted successfully and removed from all cards.",
  "deletedLabel": {
    "_id": "label_id",
    "boardId": "board_id",
    "name": "Critical Bug",
    "color": "#b04632",
    "createdAt": 1700000000000,
    "updatedAt": 1700000003000,
    "_destroy": true
  }
}
```

#### ❌ **Test Case 2: Xóa label không tồn tại**
**Request:**
```http
DELETE {{baseURL}}/labels/673b2e5c9c84711234567890
Authorization: Bearer {{token}}
```

**Expected Response (404):**
```json
{
  "message": "Label not found!"
}
```

---

## 🃏 **CARD LABELS API ENDPOINTS TESTING**

### 7. **PUT /v1/cards/:id/labels - Cập nhật labels cho card**

#### ✅ **Test Case 1: Cập nhật labels hợp lệ**
**Request:**
```http
PUT {{baseURL}}/cards/{{cardId}}/labels
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "labelIds": ["label_id_1", "label_id_2"]
}
```

**Expected Response (200):**
```json
{
  "_id": "card_id",
  "boardId": "board_id",
  "columnId": "column_id",
  "title": "Sample Card",
  "description": "Sample description",
  "labelIds": ["label_id_1", "label_id_2"],
  "labels": [
    {
      "_id": "label_id_1",
      "boardId": "board_id",
      "name": "Bug Fix",
      "color": "#eb5a46",
      "createdAt": 1700000000000,
      "updatedAt": null,
      "_destroy": false
    },
    {
      "_id": "label_id_2",
      "boardId": "board_id",
      "name": "Feature",
      "color": "#519839",
      "createdAt": 1700000001000,
      "updatedAt": null,
      "_destroy": false
    }
  ],
  "createdAt": 1700000000000,
  "updatedAt": 1700000005000,
  "_destroy": false
}
```

#### ✅ **Test Case 2: Xóa tất cả labels (mảng rỗng)**
**Request:**
```http
PUT {{baseURL}}/cards/{{cardId}}/labels
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "labelIds": []
}
```

**Expected Response (200):**
```json
{
  "_id": "card_id",
  "labelIds": [],
  "labels": [],
  "updatedAt": 1700000006000
}
```

#### ❌ **Test Case 3: Label không thuộc về board của card**
**Request:**
```http
PUT {{baseURL}}/cards/{{cardId}}/labels
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "labelIds": ["673b2e5c9c84711234567890"]
}
```

**Expected Response (400):**
```json
{
  "message": "Label 673b2e5c9c84711234567890 does not exist or does not belong to this board!"
}
```

#### ❌ **Test Case 4: LabelIds không phải là mảng**
**Request:**
```http
PUT {{baseURL}}/cards/{{cardId}}/labels
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "labelIds": "not_an_array"
}
```

**Expected Response (422):**
```json
{
  "message": "\"labelIds\" must be an array"
}
```

---

## 🔄 **INTEGRATION TESTING FLOW**

### **Workflow Testing: Tạo label → Gán cho card → Xóa label**

#### **Bước 1: Tạo label mới**
```http
POST {{baseURL}}/labels
{
  "boardId": "{{boardId}}",
  "name": "Integration Test",
  "color": "#9c27b0"
}
```
➡️ Lưu `labelId` từ response vào biến `integrationLabelId`

#### **Bước 2: Gán label cho card**
```http
PUT {{baseURL}}/cards/{{cardId}}/labels
{
  "labelIds": ["{{integrationLabelId}}"]
}
```
➡️ Verify card có label mới trong response

#### **Bước 3: Kiểm tra label có trong danh sách board**
```http
GET {{baseURL}}/labels?boardId={{boardId}}
```
➡️ Verify label có trong danh sách

#### **Bước 4: Xóa label**
```http
DELETE {{baseURL}}/labels/{{integrationLabelId}}
```
➡️ Verify response success

#### **Bước 5: Kiểm tra label đã bị remove khỏi card**
```http
GET {{baseURL}}/cards/{{cardId}}
```
➡️ Verify labelIds không còn chứa `integrationLabelId`

---

## 🚨 **ERROR CASES TESTING**

### **Authentication Errors**
- Test tất cả endpoints mà không có Authorization header
- Test với token không hợp lệ
- Test với token đã hết hạn

### **Permission Errors**
- Test truy cập labels của board không có quyền
- Test cập nhật labels với user không phải owner/member

### **Validation Errors**
- Test với các field thiếu required
- Test với dữ liệu không đúng format
- Test với ObjectId không hợp lệ

### **Business Logic Errors**
- Test gán label không thuộc board cho card
- Test xóa label không tồn tại
- Test cập nhật label không tồn tại

---

## 📊 **PERFORMANCE TESTING**

### **Load Testing**
- Test tạo nhiều labels cùng lúc
- Test cập nhật labels cho nhiều cards
- Test lấy danh sách labels của board có nhiều labels

### **Edge Cases**
- Test với board có 0 labels
- Test với card có nhiều labels (>20)
- Test với tên label Unicode/emoji

---

## ✅ **CHECKLIST TESTING**

- [ ] Tất cả endpoints trả về đúng status codes
- [ ] Response format nhất quán
- [ ] Error messages có ý nghĩa
- [ ] Authentication/Authorization hoạt động đúng
- [ ] Validation input chặt chẽ
- [ ] Business logic đúng (cascade delete, permission check)
- [ ] Performance acceptable với dữ liệu lớn
- [ ] Edge cases được handle tốt

**Lưu ý:** Thay thế các placeholder `{{variableName}}` bằng giá trị thực tế từ environment/response trước đó. 