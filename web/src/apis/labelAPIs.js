/**
 * Label API Services
 * Handle all label-related API calls with proper error handling
 */

import { toast } from 'react-toastify'
import authorizedAxiosInstance from '~/utils/authorizeAxios'
import { API_ROOT } from '~/utils/constants'

/**
 * L<PERSON>y danh sách màu có sẵn cho labels
 * @returns {Promise} Available label colors
 */
export const getLabelColorsAPI = async () => {
  try {
    const response = await authorizedAxiosInstance.get(`${API_ROOT}/v1/labels/colors`)
    return response.data
  } catch (error) {
    console.error('Get label colors error:', error)
    toast.error('Failed to load label colors')
    throw error
  }
}

/**
 * <PERSON><PERSON><PERSON> danh sách labels theo boardId
 * @param {string} boardId - ID của board
 * @returns {Promise} Labels data
 */
export const getLabelsByBoardIdAPI = async (boardId) => {
  try {
    if (!boardId) {
      throw new Error('Board ID is required')
    }
    
    const response = await authorizedAxiosInstance.get(
      `${API_ROOT}/v1/labels?boardId=${boardId}`
    )
    return response.data
  } catch (error) {
    console.error('Get labels by board ID error:', error)
    
    if (error.response?.status === 404) {
      toast.error('Board not found')
    } else if (error.response?.status === 403) {
      toast.error('You do not have permission to access this board')
    } else {
      toast.error(error.message || 'Failed to load labels')
    }
    
    throw error
  }
}

/**
 * Tạo label mới
 * @param {Object} labelData - Dữ liệu label { boardId, name, color }
 * @returns {Promise} Created label data
 */
export const createLabelAPI = async (labelData) => {
  try {
    if (!labelData.boardId || !labelData.name || !labelData.color) {
      throw new Error('Board ID, name, and color are required')
    }
    
    const response = await authorizedAxiosInstance.post(
      `${API_ROOT}/v1/labels`,
      labelData
    )
    
    toast.success('Label created successfully!')
    return response.data
  } catch (error) {
    console.error('Create label error:', error)
    
    if (error.response?.status === 403) {
      toast.error('You do not have permission to create labels in this board')
    } else if (error.response?.status === 422) {
      toast.error('Invalid label data. Please check your input.')
    } else {
      toast.error(error.message || 'Failed to create label')
    }
    
    throw error
  }
}

/**
 * Lấy thông tin label theo ID
 * @param {string} labelId - ID của label
 * @returns {Promise} Label data
 */
export const getLabelByIdAPI = async (labelId) => {
  try {
    if (!labelId) {
      throw new Error('Label ID is required')
    }
    
    const response = await authorizedAxiosInstance.get(`${API_ROOT}/v1/labels/${labelId}`)
    return response.data
  } catch (error) {
    console.error('Get label by ID error:', error)
    
    if (error.response?.status === 404) {
      toast.error('Label not found')
    } else if (error.response?.status === 403) {
      toast.error('You do not have permission to access this label')
    } else {
      toast.error(error.message || 'Failed to load label')
    }
    
    throw error
  }
}

/**
 * Cập nhật thông tin label
 * @param {string} labelId - ID của label
 * @param {Object} updateData - Dữ liệu cập nhật { name?, color? }
 * @returns {Promise} Updated label data
 */
export const updateLabelAPI = async (labelId, updateData) => {
  try {
    if (!labelId) {
      throw new Error('Label ID is required')
    }
    
    if (!updateData.name && !updateData.color) {
      throw new Error('At least name or color must be provided')
    }
    
    const response = await authorizedAxiosInstance.put(
      `${API_ROOT}/v1/labels/${labelId}`,
      updateData
    )
    
    toast.success('Label updated successfully!')
    return response.data
  } catch (error) {
    console.error('Update label error:', error)
    
    if (error.response?.status === 404) {
      toast.error('Label not found')
    } else if (error.response?.status === 403) {
      toast.error('You do not have permission to update this label')
    } else if (error.response?.status === 422) {
      toast.error('Invalid label data. Please check your input.')
    } else {
      toast.error(error.message || 'Failed to update label')
    }
    
    throw error
  }
}

/**
 * Xóa label
 * @param {string} labelId - ID của label
 * @returns {Promise} Delete result
 */
export const deleteLabelAPI = async (labelId) => {
  try {
    if (!labelId) {
      throw new Error('Label ID is required')
    }
    
    const response = await authorizedAxiosInstance.delete(`${API_ROOT}/v1/labels/${labelId}`)
    
    toast.success('Label deleted successfully!')
    return response.data
  } catch (error) {
    console.error('Delete label error:', error)
    
    if (error.response?.status === 404) {
      toast.error('Label not found')
    } else if (error.response?.status === 403) {
      toast.error('You do not have permission to delete this label')
    } else {
      toast.error(error.message || 'Failed to delete label')
    }
    
    throw error
  }
}

/**
 * Cập nhật labels cho card
 * @param {string} cardId - ID của card
 * @param {Array} labelIds - Mảng IDs của labels
 * @returns {Promise} Updated card data
 */
export const updateCardLabelsAPI = async (cardId, labelIds) => {
  try {
    if (!cardId) {
      throw new Error('Card ID is required')
    }
    
    if (!Array.isArray(labelIds)) {
      throw new Error('Label IDs must be an array')
    }
    
    const response = await authorizedAxiosInstance.put(
      `${API_ROOT}/v1/cards/${cardId}/labels`,
      { labelIds }
    )
    
    return response.data
  } catch (error) {
    console.error('Update card labels error:', error)
    
    if (error.response?.status === 404) {
      toast.error('Card not found')
    } else if (error.response?.status === 403) {
      toast.error('You do not have permission to update this card')
    } else if (error.response?.status === 400) {
      toast.error('Invalid label assignment. Labels must belong to the same board.')
    } else {
      toast.error(error.message || 'Failed to update card labels')
    }
    
    throw error
  }
}
