/**
 * Label Model for Trello-like board
 */

import <PERSON><PERSON> from 'joi'
import { ObjectId } from 'mongodb'
import { GET_DB } from '~/config/mongodb'
import { OBJECT_ID_RULE, OBJECT_ID_RULE_MESSAGE } from '~/utils/validators'

// Define Collection (Name & Schema)
const LABEL_COLLECTION_NAME = 'labels'
const LABEL_COLLECTION_SCHEMA = Joi.object({
  boardId: Joi.string().required().pattern(OBJECT_ID_RULE).message(OBJECT_ID_RULE_MESSAGE),
  name: Joi.string().required().min(3).max(50).trim().strict(),
  color: Joi.string().required(),
  
  createdAt: Joi.date().timestamp('javascript').default(Date.now),
  updatedAt: Joi.date().timestamp('javascript').default(null),
  _destroy: Joi.boolean().default(false)
})

// Chỉ định ra những Fields mà chúng ta không muốn cho phép cập nhật trong hàm update()
const INVALID_UPDATE_FIELDS = ['_id', 'boardId', 'createdAt']

const validateBeforeCreate = async (data) => {
  return await LABEL_COLLECTION_SCHEMA.validateAsync(data, { abortEarly: false })
}

const createNew = async (data) => {
  try {
    const validData = await validateBeforeCreate(data)
    const newLabelToAdd = {
      ...validData,
      boardId: new ObjectId(validData.boardId)
    }
    const createdLabel = await GET_DB().collection(LABEL_COLLECTION_NAME).insertOne(newLabelToAdd)
    return createdLabel
  } catch (error) { throw new Error(error) }
}

const findOneById = async (labelId) => {
  try {
    const result = await GET_DB().collection(LABEL_COLLECTION_NAME).findOne({ _id: new ObjectId(labelId) })
    return result
  } catch (error) { throw new Error(error) }
}

const findManyByBoardId = async (boardId) => {
  try {
    const result = await GET_DB().collection(LABEL_COLLECTION_NAME).find({
      boardId: new ObjectId(boardId),
      _destroy: false
    }).toArray()
    return result
  } catch (error) { throw new Error(error) }
}

const update = async (labelId, updateData) => {
  try {
    // Lọc những field mà chúng ta không cho phép cập nhật linh tinh
    Object.keys(updateData).forEach(fieldName => {
      if (INVALID_UPDATE_FIELDS.includes(fieldName)) {
        delete updateData[fieldName]
      }
    })

    const result = await GET_DB().collection(LABEL_COLLECTION_NAME).findOneAndUpdate(
      { _id: new ObjectId(labelId) },
      { $set: updateData },
      { returnDocument: 'after' } // sẽ trả về kết quả mới sau khi cập nhật
    )
    return result
  } catch (error) { throw new Error(error) }
}

const deleteOneById = async (labelId) => {
  try {
    const result = await GET_DB().collection(LABEL_COLLECTION_NAME).findOneAndUpdate(
      { _id: new ObjectId(labelId) },
      { $set: { _destroy: true, updatedAt: Date.now() } },
      { returnDocument: 'after' }
    )
    return result
  } catch (error) { throw new Error(error) }
}

export const labelModel = {
  LABEL_COLLECTION_NAME,
  LABEL_COLLECTION_SCHEMA,
  createNew,
  findOneById,
  findManyByBoardId,
  update,
  deleteOneById
} 