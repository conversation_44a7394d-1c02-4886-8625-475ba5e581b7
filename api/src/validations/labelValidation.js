/**
 * Label Validation - Validate input data for Label operations
 * Author: Taskflow Team
 */
import Jo<PERSON> from 'joi'
import { StatusCodes } from 'http-status-codes'
import ApiError from '~/utils/ApiError'
import { OBJECT_ID_RULE, OBJECT_ID_RULE_MESSAGE } from '~/utils/validators'
import { LABEL_COLORS } from '~/utils/constants'

/**
 * Validation cho POST /v1/labels - Tạo label mới
 */
const createNew = async (req, res, next) => {
  const correctCondition = Joi.object({
    boardId: Joi.string().required().pattern(OBJECT_ID_RULE).message(OBJECT_ID_RULE_MESSAGE).messages({
      'any.required': 'Board ID là bắt buộc',
      'string.empty': 'Board ID không được để trống'
    }),
    name: Joi.string().required().min(3).max(50).trim().strict().messages({
      'any.required': 'Tên label là bắt buộc',
      'string.empty': 'Tên label không được để trống',
      'string.min': 'Tên label phải có ít nhất 3 ký tự',
      'string.max': 'Tên label không được vượt quá 50 ký tự',
      'string.trim': 'Tên label không được có khoảng trắng ở đầu hoặc cuối'
    }),
    color: Joi.string().required().messages({
      'any.required': 'Màu label là bắt buộc',
      'string.empty': 'Màu label không được để trống'
    }).custom((value, helpers) => {
      // Kiểm tra màu có trong danh sách LABEL_COLORS hoặc là hex color hợp lệ
      const isValidLabelColor = Object.values(LABEL_COLORS).includes(value)
      const isValidHexColor = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)
      
      if (!isValidLabelColor && !isValidHexColor) {
        return helpers.error('any.invalid', { 
          message: 'Màu label phải là mã hex hợp lệ hoặc một trong các màu được định sẵn' 
        })
      }
      
      return value
    })
  })

  try {
    await correctCondition.validateAsync(req.body, { abortEarly: false })
    next()
  } catch (error) {
    next(new ApiError(StatusCodes.UNPROCESSABLE_ENTITY, new Error(error).message))
  }
}

/**
 * Validation cho PUT /v1/labels/:id - Cập nhật label
 */
const update = async (req, res, next) => {
  // Lưu ý không dùng required() trong trường hợp Update
  const correctCondition = Joi.object({
    name: Joi.string().min(3).max(50).trim().strict().messages({
      'string.min': 'Tên label phải có ít nhất 3 ký tự',
      'string.max': 'Tên label không được vượt quá 50 ký tự',
      'string.trim': 'Tên label không được có khoảng trắng ở đầu hoặc cuối'
    }),
    color: Joi.string().custom((value, helpers) => {
      // Kiểm tra màu có trong danh sách LABEL_COLORS hoặc là hex color hợp lệ
      const isValidLabelColor = Object.values(LABEL_COLORS).includes(value)
      const isValidHexColor = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(value)
      
      if (!isValidLabelColor && !isValidHexColor) {
        return helpers.error('any.invalid', { 
          message: 'Màu label phải là mã hex hợp lệ hoặc một trong các màu được định sẵn' 
        })
      }
      
      return value
    }).messages({
      'string.empty': 'Màu label không được để trống'
    })
  })

  try {
    await correctCondition.validateAsync(req.body, {
      abortEarly: false,
      allowUnknown: false // Không cho phép field không xác định
    })
    next()
  } catch (error) {
    next(new ApiError(StatusCodes.UNPROCESSABLE_ENTITY, new Error(error).message))
  }
}

/**
 * Validation cho DELETE /v1/labels/:id - Xóa label
 */
const deleteItem = async (req, res, next) => {
  const correctCondition = Joi.object({
    id: Joi.string().required().pattern(OBJECT_ID_RULE).message(OBJECT_ID_RULE_MESSAGE).messages({
      'any.required': 'ID label là bắt buộc',
      'string.empty': 'ID label không được để trống'
    })
  })

  try {
    await correctCondition.validateAsync(req.params, { abortEarly: false })
    next()
  } catch (error) {
    next(new ApiError(StatusCodes.UNPROCESSABLE_ENTITY, new Error(error).message))
  }
}

/**
 * Validation cho GET /v1/labels/:id - Lấy label theo ID
 */
const getDetails = async (req, res, next) => {
  const correctCondition = Joi.object({
    id: Joi.string().required().pattern(OBJECT_ID_RULE).message(OBJECT_ID_RULE_MESSAGE).messages({
      'any.required': 'ID label là bắt buộc',
      'string.empty': 'ID label không được để trống'
    })
  })

  try {
    await correctCondition.validateAsync(req.params, { abortEarly: false })
    next()
  } catch (error) {
    next(new ApiError(StatusCodes.UNPROCESSABLE_ENTITY, new Error(error).message))
  }
}

/**
 * Validation cho GET /v1/labels?boardId=xxx - Lấy labels theo board
 */
const getByBoardId = async (req, res, next) => {
  const correctCondition = Joi.object({
    boardId: Joi.string().required().pattern(OBJECT_ID_RULE).message(OBJECT_ID_RULE_MESSAGE).messages({
      'any.required': 'Board ID là bắt buộc trong query parameters',
      'string.empty': 'Board ID không được để trống'
    })
  })

  try {
    await correctCondition.validateAsync(req.query, { abortEarly: false })
    next()
  } catch (error) {
    next(new ApiError(StatusCodes.UNPROCESSABLE_ENTITY, new Error(error).message))
  }
}

/**
 * Validation cho PUT /v1/cards/:id/labels - Cập nhật labels của card
 */
const updateCardLabels = async (req, res, next) => {
  const correctCondition = Joi.object({
    labelIds: Joi.array().items(
      Joi.string().pattern(OBJECT_ID_RULE).message(OBJECT_ID_RULE_MESSAGE)
    ).required().messages({
      'any.required': 'Danh sách label IDs là bắt buộc',
      'array.base': 'Label IDs phải là một mảng'
    })
  })

  try {
    await correctCondition.validateAsync(req.body, { abortEarly: false })
    next()
  } catch (error) {
    next(new ApiError(StatusCodes.UNPROCESSABLE_ENTITY, new Error(error).message))
  }
}

export const labelValidation = {
  createNew,
  update,
  deleteItem,
  getDetails,
  getByBoardId,
  updateCardLabels
}
