/**
 * Label Routes - Define HTTP routes for Label operations
 * Author: Taskflow Team
 */
import express from 'express'
import { labelValidation } from '~/validations/labelValidation'
import { labelController } from '~/controllers/labelController'
import { authMiddleware } from '~/middlewares/authMiddleware'

const Router = express.Router()

/**
 * GET /v1/labels - <PERSON><PERSON><PERSON> labels theo boardId (query param)
 * GET /v1/labels/colors - Lấy danh sách màu có sẵn
 * POST /v1/labels - Tạo label mới
 */
Router.route('/')
  .get(authMiddleware.isAuthorized, labelValidation.getByBoardId, labelController.getByBoardId)
  .post(authMiddleware.isAuthorized, labelValidation.createNew, labelController.createNew)

/**
 * GET /v1/labels/colors - Lấy danh sách màu có sẵn cho labels
 * Route này phải đặt trước /:id để tránh conflict
 */
Router.route('/colors')
  .get(authMiddleware.isAuthorized, labelController.getAvailableColors)

/**
 * GET /v1/labels/:id - <PERSON><PERSON><PERSON> thông tin label theo ID
 * PUT /v1/labels/:id - Cập nhật label
 * DELETE /v1/labels/:id - Xóa label
 */
Router.route('/:id')
  .get(authMiddleware.isAuthorized, labelValidation.getDetails, labelController.getDetails)
  .put(authMiddleware.isAuthorized, labelValidation.update, labelController.update)
  .delete(authMiddleware.isAuthorized, labelValidation.deleteItem, labelController.deleteItem)

export const labelRoute = Router 