/**
 * Label Routes - Define HTTP routes for Label operations
 * Author: Taskflow Team
 */
import express from 'express'
import { labelValidation } from '~/validations/labelValidation'
import { labelController } from '~/controllers/labelController'
import { authMiddleware } from '~/middlewares/authMiddleware'

const Router = express.Router()

/**
 * GET /v1/labels/colors - L<PERSON>y danh sách màu có sẵn
 * Đặt route này trước route /:id để tránh conflict
 */
Router.get('/colors', labelController.getColors)

/**
 * GET /v1/labels - L<PERSON>y labels theo boardId (query param)
 * POST /v1/labels - Tạo label mới
 */
Router.route('/')
  .get(authMiddleware.isAuthorized, labelValidation.getByBoardId, labelController.getByBoardId)
  .post(authMiddleware.isAuthorized, labelValidation.createNew, labelController.createNew)

/**
 * GET /v1/labels/:id - <PERSON><PERSON><PERSON> thông tin label theo ID
 * PUT /v1/labels/:id - <PERSON><PERSON><PERSON> nhật label
 * DELETE /v1/labels/:id - Xóa label
 */
Router.route('/:id')
  .get(authMiddleware.isAuthorized, labelValidation.getDetails, labelController.getDetails)
  .put(authMiddleware.isAuthorized, labelValidation.update, labelController.update)
  .delete(authMiddleware.isAuthorized, labelValidation.deleteItem, labelController.deleteItem)

export const labelRoute = Router
