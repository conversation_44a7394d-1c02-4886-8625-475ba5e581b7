/**
 * Label Controller - <PERSON>le HTTP requests for Label operations
 * Author: Taskflow Team
 */
import { StatusCodes } from 'http-status-codes'
import { labelService } from '~/services/labelService'
import { LABEL_COLORS } from '~/utils/constants'

/**
 * POST /v1/labels - Tạo label mới
 */
const createNew = async (req, res, next) => {
  try {
    const userId = req.jwtDecoded._id

    // Đ<PERSON><PERSON>u hướng dữ liệu sang tầng Service
    const createdLabel = await labelService.createNewLabel(userId, req.body)

    // Trả về kết quả cho Client
    res.status(StatusCodes.CREATED).json(createdLabel)
  } catch (error) { next(error) }
}

/**
 * GET /v1/labels?boardId=xxx - Lấy labels theo board
 */
const getByBoardId = async (req, res, next) => {
  try {
    const userId = req.jwtDecoded._id
    const { boardId } = req.query

    // Kiểm tra boardId có được cung cấp không
    if (!boardId) {
      return res.status(StatusCodes.BAD_REQUEST).json({
        message: 'BoardId is required in query parameters'
      })
    }

    const labels = await labelService.getLabelsByBoardId(userId, boardId)
    res.status(StatusCodes.OK).json(labels)
  } catch (error) { next(error) }
}

/**
 * GET /v1/labels/:id - Lấy thông tin label theo ID
 */
const getDetails = async (req, res, next) => {
  try {
    const userId = req.jwtDecoded._id
    const labelId = req.params.id

    const label = await labelService.getLabelById(userId, labelId)
    res.status(StatusCodes.OK).json(label)
  } catch (error) { next(error) }
}

/**
 * PUT /v1/labels/:id - Cập nhật label
 */
const update = async (req, res, next) => {
  try {
    const userId = req.jwtDecoded._id
    const labelId = req.params.id

    const updatedLabel = await labelService.updateLabel(userId, labelId, req.body)
    res.status(StatusCodes.OK).json(updatedLabel)
  } catch (error) { next(error) }
}

/**
 * DELETE /v1/labels/:id - Xóa label
 */
const deleteItem = async (req, res, next) => {
  try {
    const userId = req.jwtDecoded._id
    const labelId = req.params.id

    const result = await labelService.deleteLabel(userId, labelId)
    res.status(StatusCodes.OK).json(result)
  } catch (error) { next(error) }
}

/**
 * GET /v1/labels/colors - Lấy danh sách màu có sẵn cho labels
 */
const getAvailableColors = async (req, res, next) => {
  try {
    const colors = LABEL_COLORS
    
    res.status(StatusCodes.OK).json({
      colors,
      message: 'Available label colors retrieved successfully'
    })
  } catch (error) { next(error) }
}

export const labelController = {
  createNew,
  getByBoardId,
  getDetails,
  update,
  deleteItem,
  getAvailableColors
} 