/* eslint-disable no-useless-catch */
/**
 * Label Service - Handle business logic for Label operations
 * Author: Taskflow Team
 */

import { labelModel } from '~/models/labelModel'
import { boardModel } from '~/models/boardModel'
import { cardModel } from '~/models/cardModel'
import ApiError from '~/utils/ApiError'
import { StatusCodes } from 'http-status-codes'
import { ObjectId } from 'mongodb'

/**
 * Ki<PERSON>m tra quyền truy cập của user đối với board
 * @param {string} userId - User ID
 * @param {string} boardId - Board ID
 * @returns {Promise<Object>} - Board nếu user có quyền truy cập
 */
const checkBoardPermission = async (userId, boardId) => {
  try {
    const board = await boardModel.findOneById(boardId)
    if (!board) {
      throw new ApiError(StatusCodes.NOT_FOUND, 'Board not found!')
    }

    // Kiểm tra user c<PERSON> phải là owner hoặc member của board không
    const isOwner = board.ownerIds.some(ownerId => ownerId.equals(new ObjectId(userId)))
    const isMember = board.memberIds.some(memberId => memberId.equals(new ObjectId(userId)))

    if (!isOwner && !isMember) {
      throw new ApiError(StatusCodes.FORBIDDEN, 'You do not have permission to access this board!')
    }

    return board
  } catch (error) { throw error }
}

/**
 * Tạo label mới với validation quyền truy cập board
 * @param {string} userId - User ID
 * @param {Object} reqBody - Request body chứa thông tin label
 * @returns {Promise<Object>} - Label được tạo mới
 */
const createNewLabel = async (userId, reqBody) => {
  try {
    // Kiểm tra quyền truy cập board trước
    await checkBoardPermission(userId, reqBody.boardId)

    // Tạo label mới
    const newLabel = {
      ...reqBody
    }
    const createdLabel = await labelModel.createNew(newLabel)
    const getNewLabel = await labelModel.findOneById(createdLabel.insertedId)

    return getNewLabel
  } catch (error) { throw error }
}

/**
 * Lấy danh sách labels của board
 * @param {string} userId - User ID
 * @param {string} boardId - Board ID
 * @returns {Promise<Array>} - Danh sách labels của board
 */
const getLabelsByBoardId = async (userId, boardId) => {
  try {
    // Kiểm tra quyền truy cập board trước
    await checkBoardPermission(userId, boardId)

    // Lấy danh sách labels của board
    const labels = await labelModel.findManyByBoardId(boardId)
    return labels
  } catch (error) { throw error }
}

/**
 * Cập nhật thông tin label (chỉ owner/member board)
 * @param {string} userId - User ID
 * @param {string} labelId - Label ID
 * @param {Object} reqBody - Request body chứa thông tin cập nhật
 * @returns {Promise<Object>} - Label đã được cập nhật
 */
const updateLabel = async (userId, labelId, reqBody) => {
  try {
    // Lấy thông tin label hiện tại
    const existingLabel = await labelModel.findOneById(labelId)
    if (!existingLabel) {
      throw new ApiError(StatusCodes.NOT_FOUND, 'Label not found!')
    }

    // Kiểm tra quyền truy cập board
    await checkBoardPermission(userId, existingLabel.boardId.toString())

    // Cập nhật label
    const updateData = {
      ...reqBody,
      updatedAt: Date.now()
    }
    const updatedLabel = await labelModel.update(labelId, updateData)

    return updatedLabel
  } catch (error) { throw error }
}

/**
 * Xóa label và remove khỏi tất cả cards
 * @param {string} userId - User ID
 * @param {string} labelId - Label ID
 * @returns {Promise<Object>} - Kết quả xóa label
 */
const deleteLabel = async (userId, labelId) => {
  try {
    // Lấy thông tin label hiện tại
    const existingLabel = await labelModel.findOneById(labelId)
    if (!existingLabel) {
      throw new ApiError(StatusCodes.NOT_FOUND, 'Label not found!')
    }

    // Kiểm tra quyền truy cập board
    await checkBoardPermission(userId, existingLabel.boardId.toString())

    // Soft delete label
    const deletedLabel = await labelModel.deleteOneById(labelId)

    // Remove label khỏi tất cả cards thuộc board này
    await removeLabelsFromAllCards(existingLabel.boardId.toString(), labelId)

    return {
      message: 'Label deleted successfully and removed from all cards.',
      deletedLabel
    }
  } catch (error) { throw error }
}

/**
 * Helper function: Remove một label khỏi tất cả cards trong board
 * @param {string} boardId - Board ID
 * @param {string} labelId - Label ID cần remove
 */
const removeLabelsFromAllCards = async (boardId, labelId) => {
  try {
    // Tìm tất cả cards có chứa labelId này trong boardId
    const { GET_DB } = await import('~/config/mongodb')
    
    const result = await GET_DB().collection(cardModel.CARD_COLLECTION_NAME).updateMany(
      { 
        boardId: new ObjectId(boardId),
        labelIds: new ObjectId(labelId),
        _destroy: false
      },
      { 
        $pull: { labelIds: new ObjectId(labelId) },
        $set: { updatedAt: Date.now() }
      }
    )

    return result
  } catch (error) { throw error }
}

/**
 * Lấy thông tin label theo ID (với kiểm tra quyền)
 * @param {string} userId - User ID
 * @param {string} labelId - Label ID
 * @returns {Promise<Object>} - Label information
 */
const getLabelById = async (userId, labelId) => {
  try {
    const label = await labelModel.findOneById(labelId)
    if (!label) {
      throw new ApiError(StatusCodes.NOT_FOUND, 'Label not found!')
    }

    // Kiểm tra quyền truy cập board
    await checkBoardPermission(userId, label.boardId.toString())

    return label
  } catch (error) { throw error }
}

export const labelService = {
  createNewLabel,
  getLabelsByBoardId,
  updateLabel,
  deleteLabel,
  getLabelById,
  checkBoardPermission
} 