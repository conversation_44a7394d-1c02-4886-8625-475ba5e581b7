# Yêu cầu phát triển tính năng Calendar cho từng Board – TaskFlow

## 1. <PERSON><PERSON><PERSON> tiê<PERSON> & bối cảnh
- Xây dựng **tính năng Calendar chỉ dành cho từng board** (không phải calendar toàn hệ thống).
- Calendar này sẽ nằm trên trang chi tiết của từng board (`/boards/:id`).
- Chỉ hiển thị các card thuộc board hiện tại, sắp xếp theo ngày hết hạn (`dueDate`).

## 2. Chức năng chính
- Calendar có thể là tab/nút chuyển view hoặc một phần riêng trong giao diện board.
- **View mặc định là tháng (month view)**, cho phép chuyển week/day nếu muốn.
- Hiển thị card có trường `dueDate` trên lịch – mỗi event thể hiện:
    - Tên card.
    - <PERSON><PERSON><PERSON> (label, nếu có).
    - Click vào event mở modal chi tiết card (dùng lại modal ActiveCard hiện có).
- **Hỗ trợ drag & drop card giữa các ngày trên calendar để đổi `dueDate` (chỉ FE, update mock data/local state).**
- Không cần filter card giữa các board.
- Nếu board không có card nào có `dueDate`, calendar sẽ trống.
- UI phải đồng bộ style với TaskFlow (Material UI, dark/light mode).

## 3. Mock Data (Format Chuẩn)
có thể tự viết mock data sao cho phù hợp với dự án 

Sử dụng dữ liệu mẫu từ web/src/apis/mock-data.js, chỉ cần thêm trường dueDate cho một số card để demo.

## 4. Hướng dẫn tích hợp UI
Calendar nằm ngay trong trang board (có thể tab hoặc nút mở view calendar).

Khi click event trên calendar, mở modal chi tiết card (ActiveCard modal).

Drag & drop card sẽ đổi ngày hết hạn (dueDate) trên FE (không gọi API).

Responsive, hoạt động tốt trên desktop/mobile.
## 5. Thư viện đề xuất
Sử dụng react-big-calendar hoặc thư viện calendar React dễ dùng nhất.

Style đồng bộ với Material UI.


## 6. Vị trí đặt code
Đặt tại: src/pages/Boards/BoardContent/CalendarView.jsx

Nếu cần tách nhỏ component: src/components/Calendar/

---

# DANH SÁCH TASK PHÁT TRIỂN TÍNH NĂNG CALENDAR

## Phase 1: Setup & Dependencies
- [x] **Task 1.1**: Cài đặt thư viện react-big-calendar và dependencies cần thiết
  - ✅ Cài đặt: `react-big-calendar`, `date-fns` (hoặc moment.js)
  - ✅ Update package.json

- [x] **Task 1.2**: Cập nhật mock data với nhiều card có dueDate đa dạng
  - ✅ Thêm dueDate cho các card trong mock-data.js
  - ✅ Đảm bảo có card quá hạn, sắp hết hạn, và trong tương lai
  - ✅ Test với các trường hợp edge case (card không có dueDate)

## Phase 2: State Management & Redux Integration
- [x] **Task 2.1**: Tạo Redux state cho Calendar view mode
  - ✅ Thêm state `calendarViewMode` trong activeBoard slice
  - ✅ Actions: `setCalendarViewMode`, `toggleCalendarView`
  - ✅ Selectors: `selectCalendarViewMode`, `selectIsCalendarActive`

- [x] **Task 2.2**: Tạo helper functions để xử lý calendar data
  - ✅ Function filter cards có dueDate từ board hiện tại
  - ✅ Function format data cho react-big-calendar
  - ✅ Function group cards theo ngày/tuần/tháng

## Phase 3: UI Components Development
- [ ] **Task 3.1**: Tạo BoardViewToggle component trong BoardBar
  - Toggle button giữa Board view và Calendar view
  - Icon: ViewKanban và CalendarMonth
  - Style đồng nhất với các chip khác trong BoardBar

- [ ] **Task 3.2**: Tạo CalendarView component chính
  - Location: `src/pages/Boards/BoardContent/CalendarView.jsx`
  - Integrate react-big-calendar
  - Support month/week/day views
  - Responsive design cho mobile/desktop

- [ ] **Task 3.3**: Tạo CalendarEvent component
  - Hiển thị card trên calendar như event
  - Thể hiện: tên card, màu label, trạng thái due date
  - Style khác nhau cho: đã hết hạn, sắp hết hạn, bình thường

- [ ] **Task 3.4**: Tạo CalendarToolbar component tùy chỉnh
  - Navigation: Previous/Next month/week/day
  - View switcher: Month/Week/Day
  - Today button
  - Style Material UI consistent

## Phase 4: Interactive Features
- [ ] **Task 4.1**: Implement calendar event click để mở ActiveCard modal
  - Click event → dispatch setActiveCardId
  - Tái sử dụng modal ActiveCard có sẵn
  - Đảm bảo modal hoạt động đúng từ calendar context

- [ ] **Task 4.2**: Implement drag & drop functionality
  - Drag event (card) giữa các ngày
  - Update dueDate trong local state/Redux
  - Visual feedback khi drag/drop
  - Validation: chỉ cho phép drop vào ngày hợp lệ

- [ ] **Task 4.3**: Implement calendar navigation và filtering
  - Navigate qua các tháng/tuần
  - Filter cards theo column (optional enhancement)
  - Search cards trong calendar view

## Phase 5: Integration & Styling
- [ ] **Task 5.1**: Tích hợp CalendarView vào BoardContent
  - Conditional rendering: Board view vs Calendar view
  - State management cho view switching
  - Maintain board data consistency

- [ ] **Task 5.2**: Style calendar với Material UI theme
  - Dark/Light mode support
  - Custom CSS cho react-big-calendar
  - Responsive breakpoints
  - Accessibility compliance

- [ ] **Task 5.3**: Implement calendar-specific utilities
  - Date formatting utilities
  - Due date status helpers (overdue, today, upcoming)
  - Color coding logic for events

## Phase 6: Enhanced Features & Polish
- [ ] **Task 6.1**: Add calendar statistics
  - Count cards by due date status
  - Show trong BoardBar hoặc Calendar header
  - Visual indicators cho workload

- [ ] **Task 6.2**: Implement calendar keyboard shortcuts
  - Arrow keys để navigate
  - Space/Enter để switch views
  - Escape để close modals

- [ ] **Task 6.3**: Add calendar export/sharing (optional)
  - Export calendar view as image
  - Share calendar URL với filtered view

## Phase 7: Testing & Optimization
- [ ] **Task 7.1**: Test responsive design
  - Mobile calendar interactions
  - Touch gestures cho navigation
  - Tablet view optimization

- [ ] **Task 7.2**: Performance optimization
  - Memoize calendar data processing
  - Lazy loading cho large datasets
  - Optimize re-renders

- [ ] **Task 7.3**: Error handling và edge cases
  - Handle cards without dueDate
  - Handle invalid dates
  - Network/loading states

## Phase 8: Documentation & Finalization
- [ ] **Task 8.1**: Component documentation
  - PropTypes/TypeScript definitions
  - Usage examples
  - API documentation

- [ ] **Task 8.2**: User experience testing
  - Test flow: Board → Calendar → Card detail
  - Test drag & drop user experience
  - Accessibility testing

- [ ] **Task 8.3**: Code review và cleanup
  - Remove unused imports/code
  - Optimize component structure
  - Final styling adjustments

---

## Ghi chú kỹ thuật:
- **Priority High**: Task 1.1, 1.2, 2.1, 3.1, 3.2, 4.1, 5.1
- **Priority Medium**: Task 2.2, 3.3, 3.4, 4.2, 5.2, 5.3
- **Priority Low**: Task 4.3, 6.x, 7.x, 8.x (enhancement features)

## Estimated Timeline: 2-3 tuần
- Week 1: Phase 1-3 (Setup + Core Components)
- Week 2: Phase 4-5 (Functionality + Integration) 
- Week 3: Phase 6-8 (Polish + Testing)




