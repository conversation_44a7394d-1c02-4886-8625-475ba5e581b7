# 🧪 UPLOAD DEBUG TEST GUIDE

## 🎯 **TEST PROCEDURE**

### **Step 1: Prepare Test**
1. Mở browser console (F12)
2. Clear console logs
3. Mở attachment modal cho một card

### **Step 2: Upload Test**
1. Click "Chọn tệp"
2. Select một file nhỏ (< 1MB)
3. Wait for upload to complete
4. **WATCH CONSOLE LOGS** carefully

### **Step 3: Expected Console Logs**

```
🔄 Modal opened - Loading attachments for cardId: xxx
🔄 loadAttachments: Starting for cardId: xxx  
✅ loadAttachments: Success, got X attachments
📊 setLocalAttachments: prev state was: X
📊 setLocalAttachments: updating to: X
📊 localAttachments state changed: X [...]

// After upload:
🔄 Upload success - Reloading attachments...
📊 localAttachments before reload: X
🔄 loadAttachments: Starting for cardId: xxx
✅ loadAttachments: Success, got X+1 attachments  
📊 setLocalAttachments: prev state was: X
📊 setLocalAttachments: updating to: X+1
📊 localAttachments state changed: X+1 [...]
✅ Upload reload completed
📊 localAttachments after 1s delay: X+1
```

### **Step 4: Diagnosis Based on Logs**

#### **🔴 Case 1: API Call Fails**
```
🔄 Upload success - Reloading attachments...
❌ loadAttachments: Failed: [error]
```
**Problem**: Backend API issue
**Solution**: Check backend logs

#### **🔴 Case 2: API Success but State Not Updated**
```
✅ loadAttachments: Success, got X+1 attachments
📊 setLocalAttachments: updating to: X+1
📊 localAttachments state changed: X [still old count]
```
**Problem**: State update issue
**Solution**: React state closure problem

#### **🔴 Case 3: State Updates but UI Doesn't**
```
📊 localAttachments state changed: X+1 [correct count]
// But UI still shows old count
```
**Problem**: UI rendering issue
**Solution**: Component re-render problem

#### **🟢 Case 4: Everything Works**
```
📊 localAttachments state changed: X+1 [correct count]
// UI shows new files immediately
```
**Problem**: None! Working correctly

## 🔧 **DEBUGGING SCENARIOS**

### **Scenario A: Console Shows Errors**
- Check network tab for failed requests
- Verify cardId is correct
- Check backend logs
- Test with different file types

### **Scenario B: API Success but UI Doesn't Update**
- Verify localAttachments count increases
- Check if setLocalAttachments callback runs
- Look for React state batching issues
- Check component key props

### **Scenario C: Everything Looks Right in Console**
- Issue might be UI rendering
- Check if attachments array has correct data
- Verify file object structure
- Test with browser refresh

## 🎯 **SPECIFIC CHECKS**

### **Check 1: Upload Response Format**
Upload API should return:
```javascript
{
  success: true,
  data: {
    uploadResults: {
      successCount: 1,
      totalFiles: 1,
      successFiles: [{ attachmentId, name, url, type, size }],
      failed: []
    }
  }
}
```

### **Check 2: GetAttachments Response Format**
Get API should return:
```javascript
{
  success: true,
  data: {
    attachments: [{ _id, name, url, type, size, uploadedAt }],
    totalCount: 2
  }
}
```

### **Check 3: LocalAttachments State Structure**
Should be array of objects:
```javascript
[
  { _id: "...", name: "file1.jpg", url: "...", type: "image/jpeg", size: 12345 },
  { _id: "...", name: "file2.pdf", url: "...", type: "application/pdf", size: 67890 }
]
```

## 🚨 **QUICK FIXES TO TRY**

### **Fix 1: Force Component Re-render**
Add key prop to attachment list component

### **Fix 2: Add State Logging**
Already added - check console logs

### **Fix 3: Delay State Check**
Already added - check 1s delay logs  

### **Fix 4: Remove Callback Conflicts**
Already done - callback disabled

---

**💡 Next Steps**: Run this test và report console logs để identify exact issue! 